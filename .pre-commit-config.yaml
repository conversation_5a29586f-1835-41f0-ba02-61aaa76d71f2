# Pre-commit hooks для SAMe проекта
# Установка: pre-commit install
# Запуск: pre-commit run --all-files

repos:
  # Базовые хуки
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--unsafe]
      - id: check-toml
      - id: check-json
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: debug-statements
      - id: name-tests-test
        args: [--pytest-test-first]
      - id: requirements-txt-fixer
      - id: fix-byte-order-marker
      - id: mixed-line-ending
        args: [--fix=lf]

  # Python форматирование
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  # Сортировка импортов
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # Линтинг Python
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear

  # Проверка типов
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports, --no-strict-optional]

  # Проверка безопасности
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, -x, tests/]

  # Проверка зависимостей на уязвимости
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check

  # Проверка секретов
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .config/.secrets.baseline]

  # Проверка YAML
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.32.0
    hooks:
      - id: yamllint
        args: [-c, .config/.yamllint.yml]

  # Проверка Markdown
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.35.0
    hooks:
      - id: markdownlint
        args: [--fix, --config, .config/.markdownlint.json]

  # Проверка Docker файлов
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # Проверка SQL
  - repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.1.4
    hooks:
      - id: sqlfluff-lint
        args: [--dialect=postgres]
      - id: sqlfluff-fix
        args: [--dialect=postgres]

  # Локальные хуки
  - repo: local
    hooks:
      # Проверка структуры проекта
      - id: check-project-structure
        name: Check project structure
        entry: python scripts/check_structure.py
        language: system
        pass_filenames: false
        always_run: true

      # Проверка конфигурации
      - id: validate-config
        name: Validate configuration
        entry: python -c "import yaml; yaml.safe_load(open('config/same_config.yaml'))"
        language: system
        files: config/.*\.yaml$

      # Проверка тестов
      - id: check-test-coverage
        name: Check test coverage
        entry: pytest --cov=src/same --cov-fail-under=80 --cov-report=term-missing
        language: system
        pass_filenames: false
        files: ^(src/same|tests)/.*\.py$

# Конфигурация для конкретных инструментов
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
