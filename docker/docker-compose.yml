networks:
  same-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:

services:
  # База данных PostgreSQL
  db:
    image: postgres:15-alpine
    container_name: same-db
    environment:
      POSTGRES_USER: same_user
      POSTGRES_PASSWORD: same_password
      POSTGRES_DB: same_db
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - same-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U same_user -d same_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis для кэширования
  redis:
    image: redis:7-alpine
    container_name: same-redis
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - same-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Основное приложение SAMe
  app:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: same-app
    environment:
      - DATABASE_URL=postgresql+asyncpg://same_user:same_password@db:5432/same_db
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=production
      - DEBUG=false
    ports:
      - "8000:8000"
    volumes:
      - ../data:/app/data
      - ../logs:/app/logs
      - ../src:/app/src  # Монтируем исходный код для быстрой разработки
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - same-network
    restart: unless-stopped

  # Frontend React приложение
  frontend:
    build:
      context: ../frontend/same-frontend
      dockerfile: Dockerfile
      target: production
    container_name: same-frontend
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:8000
      - REACT_APP_MAX_FILE_SIZE=52428800
      - REACT_APP_ENVIRONMENT=production
    ports:
      - "3000:3000"
    depends_on:
      app:
        condition: service_started
    networks:
      - same-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PgAdmin для управления БД
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: same-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - ./pgadmin:/var/lib/pgadmin
    depends_on:
      - db
    networks:
      - same-network
