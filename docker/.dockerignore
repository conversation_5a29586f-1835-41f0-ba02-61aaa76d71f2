# Build artifacts
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# IDEs
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Documentation
docs/_build/

# Logs
logs/
*.log

# Temporary files
temp/
tmp/

# Docker files (exclude from build context)
Dockerfile*
docker-compose*.yml

# Keep these essential files
!pyproject.toml
!poetry.lock
!src/
!config/
!alembic.ini
!scripts/init-db.sql