[flake8]
max-line-length = 88
max-complexity = 10
select = E,W,F,C,N
ignore = 
    E203,  # whitespace before ':'
    E501,  # line too long (handled by black)
    W503,  # line break before binary operator
    E402,  # module level import not at top of file
    F401,  # imported but unused (handled by isort)
    C901,  # too complex (handled by complexity check)

exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    .tox,
    migrations,
    alembic/versions

per-file-ignores =
    __init__.py:F401
    tests/*:S101,S106,S311
    */settings/*:S105
    */config.py:S105

# Docstring settings
docstring-convention = google
require-plugins = 
    flake8-docstrings,
    flake8-import-order,
    flake8-bugbear,
    flake8-bandit

# Import order settings
import-order-style = google
application-import-names = same
