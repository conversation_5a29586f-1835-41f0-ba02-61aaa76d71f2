# Docker Environment Variables for SAMe Project

# Database
POSTGRES_PASSWORD=same_secure_password_2024
POSTGRES_USER=same_user
POSTGRES_DB=same_db

# Redis
REDIS_PASSWORD=same_redis_password_2024

# Application
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=same_super_secret_key_change_in_production_2024
API_RELOAD=false

# Monitoring
GRAFANA_PASSWORD=same_grafana_password_2024

# MinIO
MINIO_ACCESS_KEY=same_minio_access_key
MINIO_SECRET_KEY=same_minio_secret_key_2024

# Frontend
REACT_APP_API_BASE_URL=http://localhost:8000
REACT_APP_MAX_FILE_SIZE=52428800
REACT_APP_ENVIRONMENT=production
