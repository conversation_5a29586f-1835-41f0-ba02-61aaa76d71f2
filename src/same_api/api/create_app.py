from pathlib import Path
from fastapi.staticfiles import StaticFiles
from fastapi import FastAPI
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)
from fastapi.responses import JSONResponse
from starlette.responses import HTMLResponse

from .configuration.server import Server
from .configuration.lifespan import app_lifespan as lifespan

import logging

logger = logging.getLogger("app_fastapi")


def register_static_docs_routes(app: FastAPI) -> None:

    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html() -> HTMLResponse:
        return get_swagger_ui_html(
            openapi_url=str(app.openapi_url),
            title=app.title + " - Swagger UI",
            oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
            swagger_js_url="https://unpkg.com/swagger-ui-dist@5/swagger-ui-bundle.js",
            swagger_css_url="https://unpkg.com/swagger-ui-dist@5/swagger-ui.css",
        )

    @app.get(str(app.swagger_ui_oauth2_redirect_url), include_in_schema=False)
    async def swagger_ui_redirect() -> HTMLResponse:
        return get_swagger_ui_oauth2_redirect_html()

    @app.get("/redoc", include_in_schema=False)
    async def redoc_html() -> HTMLResponse:
        return get_redoc_html(
            openapi_url=str(app.openapi_url),
            title=app.title + " - ReDoc",
            redoc_js_url="https://unpkg.com/redoc@next/bundles/redoc.standalone.js",
        )


def create_app(
    create_custom_static_urls: bool = False,
) -> FastAPI:
    
    app = FastAPI(
        title="SAMe Analog Search API", version="1.0.0",
        default_response_class=JSONResponse,
        lifespan=lifespan,
        docs_url=None if create_custom_static_urls else "/docs",
        redoc_url=None if create_custom_static_urls else "/redoc",
    )

    # app.mount("/static", StaticFiles(directory="same/api/front/static"), name="static")

    if create_custom_static_urls:
        register_static_docs_routes(app)

    logger.info("App created")

    return Server(app).get_app()


def main():
    """Entry point for running the server via poetry scripts"""
    import uvicorn
    uvicorn.run(
        "same.api.create_app:create_app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        factory=True
    )
