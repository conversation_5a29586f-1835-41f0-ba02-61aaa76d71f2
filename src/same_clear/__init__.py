"""
SAMe Clear - Модуль обработки и очистки текста

Этот модуль содержит все компоненты для предобработки, очистки,
нормализации текста и извлечения параметров.
"""

__version__ = "1.0.0"
__author__ = "igornet0"

# Импорты из text_processing
from .text_processing import (
    TextCleaner, CleaningConfig,
    Lemmatizer, LemmatizerConfig,
    TextNormalizer, NormalizerConfig,
    TextPreprocessor, PreprocessorConfig,
    Tokenizer, TokenizerConfig,
    EnhancedPreprocessor, EnhancedPreprocessorConfig,
    UnitsProcessor, UnitsConfig,
    SynonymsProcessor, SynonymsConfig,
    TechCodesProcessor, TechCodesConfig
)

# Импорты из parameter_extraction
from .parameter_extraction import (
    RegexParameterExtractor,
    ParameterParser, ParameterParserConfig,
    ParameterFormatter, ParameterAnalyzer, ParameterDataFrameUtils,
    ParameterPattern, ParameterType, ExtractedParameter
)

# Импорты утилит
from .utils import (
    case_converter
)

__all__ = [
    # Text Processing
    "TextCleaner", "CleaningConfig",
    "Lemmatizer", "LemmatizerConfig", 
    "TextNormalizer", "NormalizerConfig",
    "TextPreprocessor", "PreprocessorConfig",
    "EnhancedPreprocessor", "EnhancedPreprocessorConfig",
    "UnitsProcessor", "UnitsConfig",
    "SynonymsProcessor", "SynonymsConfig", 
    "TechCodesProcessor", "TechCodesConfig",
    
    # Parameter Extraction
    "RegexParameterExtractor",
    "ParameterParser", "ParameterParserConfig",
    "ParameterFormatter", "ParameterAnalyzer", "ParameterDataFrameUtils",
    "ParameterPattern", "ParameterType", "ExtractedParameter",
    
    # Utils
    "case_converter"
]
