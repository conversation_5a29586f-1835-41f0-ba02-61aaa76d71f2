"""
Enhanced classifier configuration for category classification
Contains improved keywords, exceptions, and priority rules for better categorization
"""

# Enhanced category keywords with more comprehensive coverage
ENHANCED_CATEGORY_KEYWORDS = {
    "средства_защиты": [
        "ледоход", "ледоходы", "ледоходов", "ледоходами",
        "каска", "каски", "касок", "касками",
        "перчатки", "перчаток", "перчатками",
        "респиратор", "респираторы", "респираторов",
        "очки", "защитные", "защитных", "защитными",
        "жилет", "жилеты", "жилетов", "жилетами",
        "сигнальный", "сигнальные", "сигнальных",
        "проф", "профессиональный", "профессиональные", "профессиональных",
        "шипы", "шипов", "шипами", "шип",
        "белая", "белый", "белые", "белых",
        "оранжевый", "оранжевые", "оранжевых",
        "прозрачные", "прозрачных", "прозрачными",
        "хлопок", "хлопковые", "хлопчатобумажные",
        "противопылевой", "противопылевые"
    ],

    "химия": [
        "сольвент", "сольвенты", "сольвентов",
        "растворитель", "растворители", "растворителей",
        "краска", "краски", "красок", "красками",
        "ацетон", "ацетона", "ацетоном",
        "спирт", "спирта", "спиртом",
        "кислота", "кислоты", "кислот", "кислотами",
        "водоэмульсионная", "водоэмульсионные",
        "этиловый", "этиловые", "этилового",
        "серная", "серные", "серной",
        "технический", "техническая", "технические",
        "универсальный", "универсальные", "универсального",
        "литров", "литры", "литр"  # Removed "л" as it's too generic
    ],

    "металлопрокат": [
        "швеллер", "швеллеры", "швеллеров",
        "уголок", "уголки", "уголков", "уголками",
        "лист", "листы", "листов", "листами",
        "арматура", "арматуры", "арматурой",
        "проволока", "проволоки", "проволокой",
        "сетка", "сетки", "сеток", "сетками",
        "стальной", "стальные", "стальных", "стальными",
        "горячекатаный", "горячекатаные", "горячекатаных",
        "сварная", "сварные", "сварных", "сварными",
        "мм", "миллиметр", "миллиметры", "миллиметров"
    ],

    "крепеж": [
        "болт", "болты", "болтов", "болтами",
        "гайка", "гайки", "гаек", "гайками",
        "винт", "винты", "винтов", "винтами",
        "шуруп", "шурупы", "шурупов", "шурупами",
        "заклепка", "заклепки", "заклепок", "заклепками",
        "дюбель", "дюбели", "дюбелей", "дюбелями",
        "оцинкованный", "оцинкованные", "оцинкованных",
        "шестигранная", "шестигранные", "шестигранных",
        "самонарезающий", "самонарезающие", "самонарезающих",
        "алюминиевая", "алюминиевые", "алюминиевых",
        "пластиковый", "пластиковые", "пластиковых",
        "дереву", "дерева", "деревом"
    ],

    "текстиль": [
        "полог", "пологи", "пологов", "пологами",
        "тент", "тенты", "тентов", "тентами",
        "ткань", "ткани", "тканей", "тканями",
        "брезент", "брезенты", "брезентов", "брезентами",
        "мешок", "мешки", "мешков", "мешками",
        "брезентовый", "брезентовые", "брезентовых",
        "защитный", "защитные", "защитных", "защитными",
        "хлопчатобумажная", "хлопчатобумажные", "хлопчатобумажных",
        "водостойкий", "водостойкие", "водостойких",
        "полипропиленовый", "полипропиленовые", "полипропиленовых"
    ]
}

# Category exceptions - items that might be misclassified
CATEGORY_EXCEPTIONS = {
    "общие_товары": {
        # Items that should NOT be classified as general goods
        "exclude_patterns": [
            r"ледоход.*\d+",
            r"сольвент.*\d+",
            r"швеллер.*\d+",
            r"болт.*\d+"
        ]
    }
}

# Category priority - higher numbers = higher priority
CATEGORY_PRIORITY = {
    "средства_защиты": 10,
    "химия": 9,
    "металлопрокат": 8,
    "крепеж": 7,
    "текстиль": 6,
    "общие_товары": 1  # Lowest priority - fallback category
}
