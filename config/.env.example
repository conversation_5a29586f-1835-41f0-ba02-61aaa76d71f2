# Пример файла переменных окружения для SAMe
# Скопируйте этот файл в .env и настройте под свою среду

# =============================================================================
# ОСНОВНЫЕ НАСТРОЙКИ СИСТЕМЫ
# =============================================================================

# Режим работы (development, production, testing)
ENVIRONMENT=development

# Уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Включить отладочный режим
DEBUG=true

# =============================================================================
# НАСТРОЙКИ БАЗЫ ДАННЫХ
# =============================================================================

# URL подключения к основной базе данных
DATABASE_URL=postgresql+asyncpg://same_user:same_password@db:5432/same_db

# URL подключения к альтернативной базе данных (для отказоустойчивости)
DATABASE_URL_ALT=postgresql+asyncpg://same_user:same_password@db:5432/same_db

# Настройки пула соединений
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_ECHO=false

# =============================================================================
# НАСТРОЙКИ API СЕРВЕРА
# =============================================================================

# Хост и порт для API
API_HOST=0.0.0.0
API_PORT=8000

# Количество воркеров (для продакшена)
API_WORKERS=1

# Включить автоперезагрузку при изменениях
API_RELOAD=true

# URL фронтенда (для CORS)
FRONTEND_URL=http://localhost:3000

# =============================================================================
# НАСТРОЙКИ БЕЗОПАСНОСТИ
# =============================================================================

# Секретный ключ для JWT токенов (сгенерируйте новый для продакшена)
SECRET_KEY=your-super-secret-key-change-this-in-production

# Алгоритм шифрования для JWT
JWT_ALGORITHM=HS256

# Время жизни токена доступа (в минутах)
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# ПУТИ К ДАННЫМ И МОДЕЛЯМ
# =============================================================================

# Базовая директория для данных
DATA_DIR=./data

# Директория для входных данных
INPUT_DIR=./data/input

# Директория для обработанных данных
PROCESSED_DIR=./data/processed

# Директория для результатов
OUTPUT_DIR=./data/output

# Директория для моделей ML
MODELS_DIR=./models

# Директория для логов
LOGS_DIR=./logs

# Временная директория
TEMP_DIR=./temp

# =============================================================================
# НАСТРОЙКИ ПОИСКА
# =============================================================================

# Метод поиска по умолчанию (fuzzy, semantic, hybrid)
DEFAULT_SEARCH_METHOD=hybrid

# Порог схожести по умолчанию
SIMILARITY_THRESHOLD=0.6

# Максимальное количество результатов на запрос
MAX_RESULTS_PER_QUERY=100

# Размер пакета для обработки
BATCH_SIZE=1000

# =============================================================================
# НАСТРОЙКИ МАШИННОГО ОБУЧЕНИЯ
# =============================================================================

# Модель SpaCy для русского языка
SPACY_MODEL=ru_core_news_lg

# Модель для семантического поиска
SEMANTIC_MODEL=sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2

# Использовать GPU для ML (true/false)
USE_GPU=false

# Размер пакета для векторизации
ML_BATCH_SIZE=32

# =============================================================================
# НАСТРОЙКИ ПРОИЗВОДИТЕЛЬНОСТИ
# =============================================================================

# Включить многопроцессорность
ENABLE_MULTIPROCESSING=true

# Максимальное количество воркеров
MAX_WORKERS=4

# Включить кэширование
ENABLE_CACHING=true

# Размер кэша
CACHE_SIZE=10000

# Время жизни кэша (в секундах)
CACHE_TTL=3600

# Лимит памяти
MEMORY_LIMIT=8GB

# =============================================================================
# НАСТРОЙКИ ИНДЕКСАЦИИ
# =============================================================================

# Тип хранилища индекса (sqlite, memory, file)
INDEX_STORAGE_BACKEND=sqlite

# Тип FAISS индекса (flat, ivf, hnsw)
FAISS_INDEX_TYPE=flat

# Параметры IVF индекса
FAISS_NLIST=100
FAISS_NPROBE=10

# Нормализовать эмбеддинги
NORMALIZE_EMBEDDINGS=true

# =============================================================================
# НАСТРОЙКИ ЭКСПОРТА
# =============================================================================

# Включить статистику в экспорт
EXPORT_INCLUDE_STATISTICS=true

# Автоматически подгонять ширину колонок в Excel
EXCEL_AUTO_ADJUST_COLUMNS=true

# Добавлять фильтры в Excel
EXCEL_ADD_FILTERS=true

# Подсвечивать высококачественные результаты
EXCEL_HIGHLIGHT_HIGH_SIMILARITY=true

# =============================================================================
# НАСТРОЙКИ МОНИТОРИНГА
# =============================================================================

# Включить метрики
ENABLE_METRICS=true

# Порт для метрик
METRICS_PORT=9090

# Интервал проверки здоровья системы (в секундах)
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# НАСТРОЙКИ УВЕДОМЛЕНИЙ
# =============================================================================

# Включить email уведомления
ENABLE_EMAIL_ALERTS=false

# SMTP сервер для отправки уведомлений
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Получатели уведомлений (через запятую)
ALERT_RECIPIENTS=<EMAIL>,<EMAIL>

# Порог ошибок для отправки алерта
ERROR_THRESHOLD=10

# Порог времени ответа для алерта (в секундах)
RESPONSE_TIME_THRESHOLD=5.0

# =============================================================================
# НАСТРОЙКИ ВНЕШНИХ СЕРВИСОВ
# =============================================================================

# Redis для кэширования (опционально)
REDIS_URL=redis://redis:6379/0

# Elasticsearch для полнотекстового поиска (опционально)
ELASTICSEARCH_URL=http://localhost:9200

# MinIO для хранения файлов (опционально)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_BUCKET=same-storage

# =============================================================================
# НАСТРОЙКИ РАЗРАБОТКИ
# =============================================================================

# Включить профилирование
ENABLE_PROFILING=false

# Размер тестовых данных
TEST_DATA_SIZE=1000

# Включить горячую перезагрузку
ENABLE_HOT_RELOAD=true

# Показывать SQL запросы в логах
SHOW_SQL_QUERIES=false

# =============================================================================
# НАСТРОЙКИ ПРОДАКШЕНА
# =============================================================================

# Максимальное количество запросов на воркер
MAX_REQUESTS=1000

# Случайное отклонение для max_requests
MAX_REQUESTS_JITTER=100

# Таймаут для запросов (в секундах)
REQUEST_TIMEOUT=30

# Включить сжатие ответов
ENABLE_GZIP=true

# =============================================================================
# НАСТРОЙКИ DOCKER
# =============================================================================

# Имя контейнера
CONTAINER_NAME=same-app

# Сеть Docker
DOCKER_NETWORK=same-network

# Том для данных
DATA_VOLUME=same-data

# =============================================================================
# НАСТРОЙКИ ТЕСТИРОВАНИЯ
# =============================================================================

# URL тестовой базы данных
TEST_DATABASE_URL=postgresql+asyncpg://test_user:test_password@localhost:5432/same_test_db

# Директория для тестовых данных
TEST_DATA_DIR=./tests/data

# Включить покрытие кода
ENABLE_COVERAGE=true

# Минимальный процент покрытия
MIN_COVERAGE=80
