# Руководство по демонстрационному Notebook SAMe

## Обзор

Файл `SAMe_Demo.ipynb` содержит полную демонстрацию работы всех модулей системы SAMe (Search Analog Model Engine). Notebook разделен на логические секции, каждая из которых демонстрирует определенную функциональность системы.

## Структура Notebook

### 1. Настройка и импорты
- Загрузка всех необходимых библиотек
- Импорт модулей системы SAMe
- Создание тестовых данных МТР
- Настройка окружения для визуализации

### 2. Демонстрация предобработки текста
- **TextCleaner**: Очистка HTML тегов, специальных символов, нормализация пробелов
- **TextNormalizer**: Нормализация единиц измерения, расширение аббревиатур
- **Lemmatizer**: Лемматизация с использованием SpaCy
- **TextPreprocessor**: Полный пайплайн предобработки

### 3. Демонстрация поисковых алгоритмов
- **FuzzySearchEngine**: Нечеткий поиск на основе TF-IDF
- **SemanticSearchEngine**: Семантический поиск с BERT-моделями
- **HybridSearchEngine**: Комбинированный подход
- Сравнение качества разных методов поиска

### 4. Демонстрация извлечения параметров
- **RegexParameterExtractor**: Извлечение через регулярные выражения
- **ParameterParser**: Комплексное извлечение параметров
- Анализ типов и качества извлеченных параметров

### 5. Демонстрация экспорта результатов
- **ExcelExporter**: Создание детальных отчетов в Excel
- Форматирование и статистика результатов

### 6. Интеграционный пример
- Полный пайплайн от запроса до результата
- Анализ производительности системы
- Статистика обработки

## Подготовка к запуску

### Требования

1. **Python 3.9+**
2. **Основные библиотеки**:
   ```bash
   pip install pandas numpy matplotlib seaborn jupyter
   ```

3. **Библиотеки для NLP**:
   ```bash
   pip install spacy
   python -m spacy download ru_core_news_lg
   ```

4. **Библиотеки для семантического поиска**:
   ```bash
   pip install sentence-transformers faiss-cpu
   ```

5. **Библиотеки для экспорта**:
   ```bash
   pip install openpyxl xlsxwriter
   ```

### Структура проекта

Убедитесь что структура проекта соответствует ожидаемой:

```
SAMe/
├── src/same/
│   ├── text_processing/
│   │   ├── text_cleaner.py
│   │   ├── lemmatizer.py
│   │   ├── normalizer.py
│   │   └── preprocessor.py
│   ├── search_engine/
│   │   ├── fuzzy_search.py
│   │   ├── semantic_search.py
│   │   ├── hybrid_search.py
│   │   └── indexer.py
│   ├── parameter_extraction/
│   │   ├── regex_extractor.py
│   │   ├── ml_extractor.py
│   │   ├── parameter_parser.py
│   │   └── parameter_utils.py
│   ├── export/
│   │   ├── excel_exporter.py
│   │   └── report_generator.py
│   ├── models/
│   │   ├── model_manager.py
│   │   └── memory_monitor.py
│   ├── data_manager/
│   │   └── DataManager.py
│   └── analog_search_engine.py
├── data/
│   └── output/
├── notebooks/demo/SAMe_Demo.ipynb
└── docs/
```

## Запуск Notebook

### Вариант 1: Jupyter Notebook

```bash
# Запуск Jupyter Notebook
jupyter notebook SAMe_Demo.ipynb
```

### Вариант 2: JupyterLab

```bash
# Запуск JupyterLab
jupyter lab SAMe_Demo.ipynb
```

### Вариант 3: VS Code

Откройте файл `SAMe_Demo.ipynb` в VS Code с установленным расширением Python.

## Порядок выполнения

### Рекомендуемый порядок

1. **Выполните все ячейки секции 1** - это настроит окружение и импортирует модули
2. **Проверьте успешность импортов** - если есть ошибки, установите недостающие зависимости
3. **Выполните секции 2-6 последовательно** - каждая секция зависит от предыдущих
4. **Обратите внимание на предупреждения** - некоторые модули могут требовать дополнительной настройки

### Возможные проблемы и решения

#### Проблема: Ошибка импорта модулей SAMe
```
ImportError: No module named 'same.text_processing'
```
**Решение**: Убедитесь что все модули созданы в соответствующих директориях

#### Проблема: Ошибка SpaCy модели
```
OSError: [E050] Can't find model 'ru_core_news_lg'
```
**Решение**: 
```bash
python -m spacy download ru_core_news_lg
```

#### Проблема: Ошибка sentence-transformers
```
ModuleNotFoundError: No module named 'sentence_transformers'
```
**Решение**:
```bash
pip install sentence-transformers
```

#### Проблема: Медленная работа семантического поиска
**Решение**: При первом запуске модель загружается из интернета, это может занять время

## Интерпретация результатов

### Метрики качества

- **Скор схожести**: 0.0-1.0, где 1.0 - точное совпадение
- **Время обработки**: Измеряется в миллисекундах для отдельных операций
- **Сжатие текста**: Показывает эффективность предобработки

### Визуализации

Notebook содержит несколько типов визуализаций:
- **Гистограммы**: Распределение параметров и скоров
- **Столбчатые диаграммы**: Сравнение методов поиска
- **Scatter plots**: Анализ сжатия текста

### Экспортированные файлы

После выполнения notebook создаются файлы:
- `data/output/same_demo_results.xlsx` - результаты поиска в Excel
- Логи обработки в консоли

## Настройка под ваши данные

### Замена тестовых данных

Чтобы использовать свои данные МТР:

1. Замените функцию `create_sample_mtr_data()` на загрузку ваших данных
2. Убедитесь что данные в формате списка строк
3. Адаптируйте тестовые запросы под вашу предметную область

### Настройка параметров

Основные параметры для настройки:

```python
# Нечеткий поиск
fuzzy_config = FuzzySearchConfig(
    similarity_threshold=0.3,  # Порог схожести
    max_results=10            # Максимум результатов
)

# Семантический поиск
semantic_config = SemanticSearchConfig(
    model_name="sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2",
    similarity_threshold=0.5
)

# Гибридный поиск
hybrid_config = HybridSearchConfig(
    fuzzy_weight=0.4,      # Вес нечеткого поиска
    semantic_weight=0.6    # Вес семантического поиска
)
```

## Производительность

### Ожидаемое время выполнения

- **Предобработка**: ~1-5мс на текст
- **Нечеткий поиск**: ~10-50мс на запрос
- **Семантический поиск**: ~50-200мс на запрос (после загрузки модели)
- **Извлечение параметров**: ~5-20мс на текст

### Оптимизация

Для улучшения производительности:
1. Используйте GPU для семантического поиска (если доступно)
2. Кэшируйте результаты предобработки
3. Настройте размер пакетов для обработки
4. Используйте более легкие модели для больших каталогов

## Расширение функциональности

### Добавление новых типов параметров

1. Расширьте `ParameterType` enum
2. Добавьте новые паттерны в `RegexParameterExtractor`
3. Обновите конфигурацию парсера

### Интеграция с внешними системами

Notebook можно адаптировать для:
- Загрузки данных из баз данных
- Интеграции с ERP системами
- Автоматической обработки файлов каталогов

## Поддержка

При возникновении проблем:

1. Проверьте логи в консоли Jupyter
2. Убедитесь что все зависимости установлены
3. Проверьте структуру проекта
4. Обратитесь к документации модулей в `docs/`

## Дальнейшие шаги

После успешного выполнения демонстрации:

1. Изучите API документацию в `docs/API_REFERENCE.md`
2. Настройте систему под ваши данные
3. Интегрируйте с вашими рабочими процессами
4. Рассмотрите развертывание в продакшене

---

**Примечание**: Этот notebook предназначен для демонстрации возможностей системы. Для продакшена рекомендуется использовать API интерфейс и оптимизировать конфигурацию под конкретные задачи.
